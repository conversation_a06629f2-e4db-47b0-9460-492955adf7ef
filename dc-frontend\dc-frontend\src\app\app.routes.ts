import { Routes } from '@angular/router';
import { InicioComponent } from './components/inicio/inicio.component';
import { LoginComponent } from './components/login/login.component';
import { HomeComponent } from './components/home/<USER>';
import { UsuariosComponent } from './components/usuarios/usuarios.component'; 
import { RepresentantesComponent } from './components/representantes/representantes.component';
import { FormacionesPoliticasComponent } from './components/formaciones-politicas/formaciones-politicas.component'; 
import { PlantillasComponent } from './components/plantillas/plantillas.component';
import { authInitGuard } from './guards/auth.guard';
import { CircunscripcionesComponent } from './components/circunscripciones/circunscripciones.component';
import { ResetComponent } from './components/reset/reset.component';
import { ConvocatoriaComponent } from './components/convocatoria/convocatoria.component';
import { ValidacionCandidaturasComponent } from './components/validacion-candidaturas/validacion-candidaturas.component';
import { CandidaturasComponent } from './components/candidaturas/candidaturas.component';
import { CandidatosPorCandidaturaComponent } from './components/candidatos-por-candidatura/candidatos-por-candidatura.component';

export const routes: Routes = [
  { path: '', component: InicioComponent },
  //{ path: 'login', component: LoginComponent }
  { path: 'admin/usuarios', component: UsuariosComponent, canActivate: [authInitGuard] },
  { path: 'admin/representantes', component: RepresentantesComponent, canActivate: [authInitGuard] },
  { path: 'admin/formaciones-politicas', component: FormacionesPoliticasComponent, canActivate: [authInitGuard] },
  { path: 'admin/plantillas', component: PlantillasComponent, canActivate: [authInitGuard] }, 
  { path: 'admin/circunscripciones', component: CircunscripcionesComponent, canActivate: [authInitGuard] },
  { path: 'home', component: HomeComponent, canActivate: [authInitGuard] },
  { path: 'candidaturas', component: CandidaturasComponent, canActivate: [authInitGuard] },
  { path: 'candidaturas/:id/candidatos', component: CandidatosPorCandidaturaComponent, canActivate: [authInitGuard] },
  { path: 'admin/validacion-candidaturas', component: ValidacionCandidaturasComponent, canActivate: [authInitGuard] },
  { path: 'admin/reset', component: ResetComponent, canActivate: [authInitGuard] },
  { path: 'admin/convocatoria', component: ConvocatoriaComponent, canActivate: [authInitGuard] },
];
