import { Injectable } from '@angular/core';

/**
 * Servicio para gestión segura de tokens
 * Permite SSO entre pestañas con localStorage + limpieza automática por seguridad
 */
@Injectable({
  providedIn: 'root'
})
export class SecureTokenService {
  
  // Almacenamiento en memoria (se pierde al cerrar/recargar la página)
  private memoryStorage: { [key: string]: any } = {};

  constructor() {
    // Para SSO: NO limpiar localStorage automáticamente
    // Solo limpiar tokens expirados o inválidos
    this.setupTokenCleanup();
  }

  /**
   * Almacena un valor en memoria
   */
  setItem(key: string, value: any): void {
    this.memoryStorage[key] = value;
  }

  /**
   * Obtiene un valor de memoria
   */
  getItem(key: string): any {
    return this.memoryStorage[key];
  }

  /**
   * Elimina un valor de memoria
   */
  removeItem(key: string): void {
    delete this.memoryStorage[key];
  }

  /**
   * Limpia todo el almacenamiento en memoria
   */
  clear(): void {
    this.memoryStorage = {};
  }

  /**
   * Verifica si existe una clave en memoria
   */
  hasItem(key: string): boolean {
    return key in this.memoryStorage;
  }

  /**
   * Obtiene todas las claves almacenadas
   */
  getKeys(): string[] {
    return Object.keys(this.memoryStorage);
  }

  /**
   * Configura limpieza automática de tokens para seguridad
   * Limpia tokens al cerrar la ventana/pestaña pero permite SSO
   */
  private setupTokenCleanup(): void {
    // Limpiar tokens cuando se cierre la ventana/pestaña
    window.addEventListener('beforeunload', () => {
      this.clearExpiredTokens();
    });

    // Limpiar tokens expirados cada 5 minutos
    setInterval(() => {
      this.clearExpiredTokens();
    }, 5 * 60 * 1000);
  }

  /**
   * Limpia solo tokens expirados o inválidos (mantiene SSO)
   */
  private clearExpiredTokens(): void {
    const keysToCheck = ['kc-token', 'kc_token', 'keycloak-token'];

    keysToCheck.forEach(key => {
      const token = localStorage.getItem(key);
      if (token && this.isTokenExpired(token)) {
        localStorage.removeItem(key);
        console.log(`Token expirado eliminado: ${key}`);
      }
    });
  }

  /**
   * Verifica si un token JWT está expirado
   */
  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Math.floor(Date.now() / 1000);
      return payload.exp < now;
    } catch {
      return true; // Si no se puede decodificar, considerarlo expirado
    }
  }

  /**
   * Fuerza limpieza completa de tokens (para logout manual)
   */
  forceCleanAllTokens(): void {
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('kc-') ||
        key.includes('keycloak') ||
        key.includes('token') ||
        key.includes('refresh')
      )) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log(`Token eliminado manualmente: ${key}`);
    });
  }

  /**
   * Migra tokens desde localStorage a memoria (si existen)
   * y luego los elimina del localStorage
   */
  migrateFromLocalStorage(): void {
    const tokenKeys = ['kc_token', 'kc_refreshToken', 'kc_idToken'];
    
    tokenKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        this.setItem(key, value);
        localStorage.removeItem(key);
        console.log(`Token migrado a memoria: ${key}`);
      }
    });
  }

  /**
   * Migra tokens desde localStorage a sessionStorage (si existen)
   * y luego los elimina del localStorage
   */
  migrateFromLocalStorageToSession(): void {
    const tokenKeys = ['kc_token', 'kc_refreshToken', 'kc_idToken'];

    tokenKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        sessionStorage.setItem(key, value);
        localStorage.removeItem(key);
        console.log(`Token migrado a sessionStorage: ${key}`);
      }
    });
  }

  /**
   * Métodos para trabajar con sessionStorage
   */
  setSessionItem(key: string, value: any): void {
    sessionStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
  }

  getSessionItem(key: string): any {
    const value = sessionStorage.getItem(key);
    if (!value) return null;

    try {
      return JSON.parse(value);
    } catch {
      return value; // Si no es JSON, devolver como string
    }
  }

  removeSessionItem(key: string): void {
    sessionStorage.removeItem(key);
  }

  clearSessionStorage(): void {
    sessionStorage.clear();
  }

  /**
   * Obtiene información sobre el almacenamiento actual
   */
  getStorageInfo(): { memoryKeys: string[], localStorageTokens: string[], sessionStorageTokens: string[] } {
    const memoryKeys = this.getKeys();
    const localStorageTokens: string[] = [];
    const sessionStorageTokens: string[] = [];

    // Revisar localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('kc-') ||
        key.includes('keycloak') ||
        key.includes('token')
      )) {
        localStorageTokens.push(key);
      }
    }

    // Revisar sessionStorage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && (
        key.startsWith('kc-') ||
        key.includes('keycloak') ||
        key.includes('token')
      )) {
        sessionStorageTokens.push(key);
      }
    }

    return { memoryKeys, localStorageTokens, sessionStorageTokens };
  }
}
