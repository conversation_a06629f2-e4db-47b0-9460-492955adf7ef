import { Injectable } from '@angular/core';

/**
 * Servicio para almacenar tokens de forma segura
 * Soporta memoria y sessionStorage (más seguro que localStorage)
 */
@Injectable({
  providedIn: 'root'
})
export class SecureTokenService {
  
  // Almacenamiento en memoria (se pierde al cerrar/recargar la página)
  private memoryStorage: { [key: string]: any } = {};

  constructor() {
    // Limpiar cualquier token existente en localStorage al inicializar
    this.clearLocalStorageTokens();
    // Migrar tokens de localStorage a sessionStorage si existen
    this.migrateFromLocalStorageToSession();
  }

  /**
   * Almacena un valor en memoria
   */
  setItem(key: string, value: any): void {
    this.memoryStorage[key] = value;
  }

  /**
   * Obtiene un valor de memoria
   */
  getItem(key: string): any {
    return this.memoryStorage[key];
  }

  /**
   * Elimina un valor de memoria
   */
  removeItem(key: string): void {
    delete this.memoryStorage[key];
  }

  /**
   * Limpia todo el almacenamiento en memoria
   */
  clear(): void {
    this.memoryStorage = {};
  }

  /**
   * Verifica si existe una clave en memoria
   */
  hasItem(key: string): boolean {
    return key in this.memoryStorage;
  }

  /**
   * Obtiene todas las claves almacenadas
   */
  getKeys(): string[] {
    return Object.keys(this.memoryStorage);
  }

  /**
   * Limpia tokens de Keycloak del localStorage
   * (por seguridad, para evitar que se almacenen ahí)
   */
  private clearLocalStorageTokens(): void {
    const keysToRemove: string[] = [];
    
    // Buscar claves relacionadas con Keycloak
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('kc-') || 
        key.includes('keycloak') || 
        key.includes('token') ||
        key.includes('refresh')
      )) {
        keysToRemove.push(key);
      }
    }
    
    // Eliminar las claves encontradas
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log(`Token eliminado de localStorage: ${key}`);
    });
  }

  /**
   * Migra tokens desde localStorage a memoria (si existen)
   * y luego los elimina del localStorage
   */
  migrateFromLocalStorage(): void {
    const tokenKeys = ['kc_token', 'kc_refreshToken', 'kc_idToken'];
    
    tokenKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        this.setItem(key, value);
        localStorage.removeItem(key);
        console.log(`Token migrado a memoria: ${key}`);
      }
    });
  }

  /**
   * Migra tokens desde localStorage a sessionStorage (si existen)
   * y luego los elimina del localStorage
   */
  migrateFromLocalStorageToSession(): void {
    const tokenKeys = ['kc_token', 'kc_refreshToken', 'kc_idToken'];

    tokenKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        sessionStorage.setItem(key, value);
        localStorage.removeItem(key);
        console.log(`Token migrado a sessionStorage: ${key}`);
      }
    });
  }

  /**
   * Métodos para trabajar con sessionStorage
   */
  setSessionItem(key: string, value: any): void {
    sessionStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
  }

  getSessionItem(key: string): any {
    const value = sessionStorage.getItem(key);
    if (!value) return null;

    try {
      return JSON.parse(value);
    } catch {
      return value; // Si no es JSON, devolver como string
    }
  }

  removeSessionItem(key: string): void {
    sessionStorage.removeItem(key);
  }

  clearSessionStorage(): void {
    sessionStorage.clear();
  }

  /**
   * Obtiene información sobre el almacenamiento actual
   */
  getStorageInfo(): { memoryKeys: string[], localStorageTokens: string[], sessionStorageTokens: string[] } {
    const memoryKeys = this.getKeys();
    const localStorageTokens: string[] = [];
    const sessionStorageTokens: string[] = [];

    // Revisar localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('kc-') ||
        key.includes('keycloak') ||
        key.includes('token')
      )) {
        localStorageTokens.push(key);
      }
    }

    // Revisar sessionStorage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && (
        key.startsWith('kc-') ||
        key.includes('keycloak') ||
        key.includes('token')
      )) {
        sessionStorageTokens.push(key);
      }
    }

    return { memoryKeys, localStorageTokens, sessionStorageTokens };
  }
}
