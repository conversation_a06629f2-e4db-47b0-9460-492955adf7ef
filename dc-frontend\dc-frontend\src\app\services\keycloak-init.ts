import { KeycloakService } from 'keycloak-angular';

export function initializeKeycloak(keycloakService: KeycloakService) {
  return async () => {
    // Solo inicializar Keycloak si estamos en navegador
    if (typeof window === 'undefined') return true;

    try {
      await keycloakService.init({
        config: {
          url: 'https://158.179.220.246:8453',
          realm: 'parlamento',
          clientId: 'datosCandidatos'
        },
        initOptions: {
          onLoad: 'check-sso',
          checkLoginIframe: false,
          // Configurar para almacenamiento en sessionStorage (persiste durante la sesión)
          flow: 'standard',
          responseMode: 'fragment',
          // Usar sessionStorage en lugar de localStorage para mayor seguridad
          storage: 'sessionStorage',
          silentCheckSsoRedirectUri: window.location.origin + '/assets/silent-check-sso.html',
          // Configuración adicional para seguridad
          pkceMethod: 'S256'
        },
        bearerExcludedUrls: ['/assets'],
      });

      setInterval(async () => {
        try {
          await keycloakService.updateToken(30);
        } catch {
          console.error('Error al actualizar el token de Keycloak, redirigiendo a inicio');
          keycloakService.login();
        }
      }, 10000); // Actualiza el token cada 10 segundos
    } catch (error) {
      console.error('Error al inicializar Keycloak:', error);
      return false; // Si hay un error, no se inicializa correctamente
    }

    return true; // Si todo va bien, se inicializa correctamente
  };
}