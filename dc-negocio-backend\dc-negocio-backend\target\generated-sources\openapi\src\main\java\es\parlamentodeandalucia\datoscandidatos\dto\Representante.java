package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Representante
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-14T09:54:46.296710600+02:00[Europe/Madrid]")
public class Representante {

  private Integer id;

  private String nombre;

  private String email;

  private String circunscripcion;

  public Representante id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Representante nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Get nombre
   * @return nombre
  */
  
  @Schema(name = "nombre", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("nombre")
  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public Representante email(String email) {
    this.email = email;
    return this;
  }

  /**
   * Get email
   * @return email
  */
  
  @Schema(name = "email", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("email")
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public Representante circunscripcion(String circunscripcion) {
    this.circunscripcion = circunscripcion;
    return this;
  }

  /**
   * Get circunscripcion
   * @return circunscripcion
  */
  
  @Schema(name = "circunscripcion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("circunscripcion")
  public String getCircunscripcion() {
    return circunscripcion;
  }

  public void setCircunscripcion(String circunscripcion) {
    this.circunscripcion = circunscripcion;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Representante representante = (Representante) o;
    return Objects.equals(this.id, representante.id) &&
        Objects.equals(this.nombre, representante.nombre) &&
        Objects.equals(this.email, representante.email) &&
        Objects.equals(this.circunscripcion, representante.circunscripcion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, nombre, email, circunscripcion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Representante {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    circunscripcion: ").append(toIndentedString(circunscripcion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

