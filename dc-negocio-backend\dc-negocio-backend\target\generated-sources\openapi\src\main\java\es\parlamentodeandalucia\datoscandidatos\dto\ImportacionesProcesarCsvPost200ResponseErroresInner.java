package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * ImportacionesProcesarCsvPost200ResponseErroresInner
 */

@JsonTypeName("_importaciones_procesar_csv_post_200_response_errores_inner")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-14T09:53:04.232407600+02:00[Europe/Madrid]")
public class ImportacionesProcesarCsvPost200ResponseErroresInner {

  private Long linea;

  private String campo;

  private String error;

  public ImportacionesProcesarCsvPost200ResponseErroresInner linea(Long linea) {
    this.linea = linea;
    return this;
  }

  /**
   * Número de línea en el archivo donde ocurrió el error
   * @return linea
  */
  
  @Schema(name = "linea", description = "Número de línea en el archivo donde ocurrió el error", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("linea")
  public Long getLinea() {
    return linea;
  }

  public void setLinea(Long linea) {
    this.linea = linea;
  }

  public ImportacionesProcesarCsvPost200ResponseErroresInner campo(String campo) {
    this.campo = campo;
    return this;
  }

  /**
   * Campo o tipo de error (ej. PROCESAMIENTO)
   * @return campo
  */
  
  @Schema(name = "campo", description = "Campo o tipo de error (ej. PROCESAMIENTO)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("campo")
  public String getCampo() {
    return campo;
  }

  public void setCampo(String campo) {
    this.campo = campo;
  }

  public ImportacionesProcesarCsvPost200ResponseErroresInner error(String error) {
    this.error = error;
    return this;
  }

  /**
   * Descripción detallada del error incluyendo los datos específicos de la entrada que falló
   * @return error
  */
  
  @Schema(name = "error", description = "Descripción detallada del error incluyendo los datos específicos de la entrada que falló", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("error")
  public String getError() {
    return error;
  }

  public void setError(String error) {
    this.error = error;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ImportacionesProcesarCsvPost200ResponseErroresInner importacionesProcesarCsvPost200ResponseErroresInner = (ImportacionesProcesarCsvPost200ResponseErroresInner) o;
    return Objects.equals(this.linea, importacionesProcesarCsvPost200ResponseErroresInner.linea) &&
        Objects.equals(this.campo, importacionesProcesarCsvPost200ResponseErroresInner.campo) &&
        Objects.equals(this.error, importacionesProcesarCsvPost200ResponseErroresInner.error);
  }

  @Override
  public int hashCode() {
    return Objects.hash(linea, campo, error);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ImportacionesProcesarCsvPost200ResponseErroresInner {\n");
    sb.append("    linea: ").append(toIndentedString(linea)).append("\n");
    sb.append("    campo: ").append(toIndentedString(campo)).append("\n");
    sb.append("    error: ").append(toIndentedString(error)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

