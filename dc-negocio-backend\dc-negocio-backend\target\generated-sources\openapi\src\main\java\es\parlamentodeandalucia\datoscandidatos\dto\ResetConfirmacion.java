package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * ResetConfirmacion
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-14T09:53:04.232407600+02:00[Europe/Madrid]")
public class ResetConfirmacion {

  private Boolean confirmacion1;

  private Boolean confirmacion2;

  public ResetConfirmacion() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ResetConfirmacion(Boolean confirmacion1, Boolean confirmacion2) {
    this.confirmacion1 = confirmacion1;
    this.confirmacion2 = confirmacion2;
  }

  public ResetConfirmacion confirmacion1(Boolean confirmacion1) {
    this.confirmacion1 = confirmacion1;
    return this;
  }

  /**
   * Get confirmacion1
   * @return confirmacion1
  */
  @NotNull 
  @Schema(name = "confirmacion1", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("confirmacion1")
  public Boolean getConfirmacion1() {
    return confirmacion1;
  }

  public void setConfirmacion1(Boolean confirmacion1) {
    this.confirmacion1 = confirmacion1;
  }

  public ResetConfirmacion confirmacion2(Boolean confirmacion2) {
    this.confirmacion2 = confirmacion2;
    return this;
  }

  /**
   * Get confirmacion2
   * @return confirmacion2
  */
  @NotNull 
  @Schema(name = "confirmacion2", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("confirmacion2")
  public Boolean getConfirmacion2() {
    return confirmacion2;
  }

  public void setConfirmacion2(Boolean confirmacion2) {
    this.confirmacion2 = confirmacion2;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResetConfirmacion resetConfirmacion = (ResetConfirmacion) o;
    return Objects.equals(this.confirmacion1, resetConfirmacion.confirmacion1) &&
        Objects.equals(this.confirmacion2, resetConfirmacion.confirmacion2);
  }

  @Override
  public int hashCode() {
    return Objects.hash(confirmacion1, confirmacion2);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResetConfirmacion {\n");
    sb.append("    confirmacion1: ").append(toIndentedString(confirmacion1)).append("\n");
    sb.append("    confirmacion2: ").append(toIndentedString(confirmacion2)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

