<div class="layout-container">
  <app-header class="header-fixed"></app-header>
  <p-toast></p-toast>

  <div class="page-wrapper scrollable-content">
    <div class="breadcrumbs">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16" height="16" style="margin-right:3px"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>
      <span class="separator">&gt;</span>
      <span>Administración</span>
      <span class="separator">&gt;</span>
      <a class="crumb-link" [routerLink]="['/candidaturas']">Candidaturas</a>
      <span class="separator">&gt;</span>
      <strong>Candidatos</strong>
    </div>

    <div class="title-section">
      <div class="title-with-back">
        <button class="btn-back" (click)="goBack()">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
          </svg>
        </button>
        <h1>Candidatos por candidatura</h1>
      </div>
      <div class="actions">
        <button pButton type="button" label="Añadir nuevo" icon="pi pi-plus" (click)="abrirModalCrear()"></button>
      </div>
    </div>

    <div class="usuarios-page-container">
      <!-- Cabecera con info de la candidatura seleccionada -->
      <div class="header-summary">
        <div class="summary-field"><label>Formación política:</label><span>{{ candidatura?.formacionPolitica?.nombre || candidatura?.formacionPolitica?.siglas || '-' }}</span></div>
        <div class="summary-field"><label>Circunscripción:</label><span>{{ candidatura?.circunscripcion?.nombre || '-' }}</span></div>
        <div class="summary-field"><label>Orden:</label><span>{{ candidatura?.orden || '-' }}</span></div>
      </div>

      <div class="filter-actions-bar">
        <button class="btn-text" (click)="limpiarFiltros()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px" height="16px">
            <path d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z"/>
          </svg>
          Limpiar filtros
        </button>
        <div class="dropdown-masivo">
          <p-menu #massMenu [model]="massItems" [popup]="true" [appendTo]="'body'" [baseZIndex]="11000" (onShow)="isMassMenuOpen=true" (onHide)="isMassMenuOpen=false"></p-menu>
          <button class="btn-massive" [disabled]="selected.length===0"
                  [ngClass]="{'is-disabled': selected.length===0, 'is-open': isMassMenuOpen}"
                  (click)="massMenu.toggle($event)">
            Acciones masivas
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 10l5 5 5-5H7z"/>
            </svg>
          </button>
        </div>
      </div>

      <p-table #dt [value]="candidatos" [loading]="loading" [paginator]="true" [rows]="10" [rowsPerPageOptions]="[5,10,20]" [rowHover]="true" [(selection)]="selected" dataKey="id"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown">
        <ng-template pTemplate="header">
          <tr>
            <th style="width:3rem"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
            <th pSortableColumn="orden">Orden <p-sortIcon field="orden"></p-sortIcon>
              <p-columnFilter field="orden" matchMode="equals" display="menu" type="numeric"></p-columnFilter>
            </th>
            <th pSortableColumn="nombre">Nombre <p-sortIcon field="nombre"></p-sortIcon>
              <p-columnFilter field="nombre" matchMode="contains" display="menu" type="text"></p-columnFilter>
            </th>
            <th pSortableColumn="apellido1">Primer Apellido <p-sortIcon field="apellido1"></p-sortIcon>
              <p-columnFilter field="apellido1" matchMode="contains" display="menu" type="text"></p-columnFilter>
            </th>
            <th pSortableColumn="apellido2">Segundo Apellido <p-sortIcon field="apellido2"></p-sortIcon>
              <p-columnFilter field="apellido2" matchMode="contains" display="menu" type="text"></p-columnFilter>
            </th>
            <th>Tipo</th>
            <th>Estado</th>
            <th style="width:140px">Acciones</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-row>
          <tr>
            <td><p-tableCheckbox [value]="row"></p-tableCheckbox></td>
            <td>{{ row.orden }}</td>
            <td>{{ row.nombre }}</td>
            <td>{{ row.apellido1 }}</td>
            <td>{{ row.apellido2 }}</td>
            <td><p-tag [severity]="'info'" [value]="row.tipo?.valor || '-' "></p-tag></td>
            <td><p-tag [severity]="getEstadoSeverity(row.estado)" [value]="row.estado?.valor || '-' "></p-tag></td>
            <td class="acciones-cell">
              <p-menu #rowMenu [model]="rowItems" [popup]="true" [appendTo]="'body'" [baseZIndex]="11000" (onShow)="openRowId=row.id" (onHide)="openRowId=null"></p-menu>
              <button class="btn-text" [ngClass]="{'is-open': openRowId===row.id}" (click)="setCurrentRow(row, rowMenu, $event)">Acciones
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr><td colspan="8" class="p-text-center">No hay candidatos.</td></tr>
        </ng-template>
      </p-table>
    </div>

    <!-- Modal Crear/Editar Candidato -->
    <p-dialog [(visible)]="showDialog" [modal]="true" [draggable]="false" [style]="{width:'520px'}" [breakpoints]="{'960px':'95vw'}" [appendTo]="'body'" [autoZIndex]="true" [baseZIndex]="11000">
      <ng-template pTemplate="header">{{ isEditing ? 'Editar candidato' : 'Añadir candidato' }}</ng-template>
      <ng-template pTemplate="content">
        <div class="field"><label>Formación política</label><input pInputText [disabled]="true" [ngModel]="candidatura?.formacionPolitica?.nombre || candidatura?.formacionPolitica?.siglas" /></div>
        <div class="field"><label>Circunscripción</label><input pInputText [disabled]="true" [ngModel]="candidatura?.circunscripcion?.nombre" /></div>
        <div class="field"><label>Orden candidatura</label><p-inputNumber [disabled]="true" [ngModel]="candidatura?.orden"></p-inputNumber></div>
        <div class="field"><label>Nombre</label><input pInputText [(ngModel)]="nuevoCandidato.nombre" /></div>
        <div class="field"><label>Primer Apellido</label><input pInputText [(ngModel)]="nuevoCandidato.apellido1" /></div>
        <div class="field"><label>Segundo Apellido</label><input pInputText [(ngModel)]="nuevoCandidato.apellido2" /></div>
        <div class="field"><label>Titularidad</label><p-select [options]="tipos" optionLabel="valor" optionValue="id" [(ngModel)]="selectedTipoId" placeholder="Seleccionar" [appendTo]="'body'"></p-select></div>
        <div class="field"><label>Orden Candidato</label><p-inputNumber [(ngModel)]="nuevoCandidato.orden" [min]="1"></p-inputNumber></div>
        <div class="field"><label>Estado</label><p-select [options]="estados" optionLabel="valor" optionValue="id" [(ngModel)]="selectedEstadoId" placeholder="Seleccionar" [appendTo]="'body'"></p-select></div>
      </ng-template>
      <ng-template pTemplate="footer">
        <button pButton type="button" label="Cancelar" class="p-button-text" (click)="showDialog=false"></button>
        <button pButton type="button" label="Guardar" (click)="guardar()"></button>
      </ng-template>
    </p-dialog>

    <p-confirmDialog></p-confirmDialog>
    <p-toast></p-toast>

    <app-footer class="footer-fixed"></app-footer>
  </div>
</div>
