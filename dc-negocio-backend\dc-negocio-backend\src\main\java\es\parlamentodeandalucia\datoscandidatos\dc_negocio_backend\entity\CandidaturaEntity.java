package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.util.List;

@Entity
@Table(name = "dac_t_candidatura")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class CandidaturaEntity {

    @Id
    @GeneratedValue(strategy = jakarta.persistence.GenerationType.IDENTITY)
    @Column(name = "dac_id_candidatura")
    private Long id;

    @Column(name = "dac_in_orden")
    private Integer orden;

    @Column(name = "dac_tx_usuario_creacion")
    private String usuarioCreacion;

    @Column(name = "dac_fh_creacion")
    private java.time.LocalDate fechaCreacion;

    @Column(name = "dac_tx_usuario_validacion")
    private String usuarioValidacion;

    @Column(name = "dac_fh_validacion")
    private java.time.LocalDate fechaValidacion;

    @Column(name = "dac_tx_comentario_validacion", length = 1024)
    private String comentarioValidacion;

    @Column(name = "dac_tx_observacion_rechazo", length = 1024)
    private String observacionRechazo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dac_fk_formacion_politica")
    private FormacionPoliticaEntity formacionPolitica;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "dac_fk_circunscripcion")
    private CircunscripcionEntity circunscripcion;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dac_fk_estado_candidatura")
    private EstadoCandidaturaEntity estadoCandidatura;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dac_fk_tipo_candidatura")
    private TipoCandidaturaEntity tipoCandidatura;

    // Relación con candidatos de esta candidatura
    @OneToMany(mappedBy = "candidatura", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CandidatoEntity> candidatos;

    // Constructor por defecto
    public CandidaturaEntity() {}

    // Constructor con parámetros principales
    public CandidaturaEntity(FormacionPoliticaEntity formacionPolitica, CircunscripcionEntity circunscripcion,
                           Integer orden, String usuarioCreacion) {
        this.formacionPolitica = formacionPolitica;
        this.circunscripcion = circunscripcion;
        this.orden = orden;
        this.usuarioCreacion = usuarioCreacion;
        this.fechaCreacion = java.time.LocalDate.now();
    }



// Getters y Setters


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrden() {
        return orden;
    }

    public void setOrden(Integer orden) {
        this.orden = orden;
    }

    public String getUsuarioCreacion() {
        return usuarioCreacion;
    }

    public void setUsuarioCreacion(String usuarioCreacion) {
        this.usuarioCreacion = usuarioCreacion;
    }

    public java.time.LocalDate getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(java.time.LocalDate fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public String getUsuarioValidacion() {
        return usuarioValidacion;
    }

    public void setUsuarioValidacion(String usuarioValidacion) {
        this.usuarioValidacion = usuarioValidacion;
    }

    public java.time.LocalDate getFechaValidacion() {
        return fechaValidacion;
    }

    public void setFechaValidacion(java.time.LocalDate fechaValidacion) {
        this.fechaValidacion = fechaValidacion;
    }

    public String getComentarioValidacion() {
        return comentarioValidacion;
    }

    public void setComentarioValidacion(String comentarioValidacion) {
        this.comentarioValidacion = comentarioValidacion;
    }

    public String getObservacionRechazo() {
        return observacionRechazo;
    }

    public void setObservacionRechazo(String observacionRechazo) {
        this.observacionRechazo = observacionRechazo;
    }

    public CircunscripcionEntity getCircunscripcion() {
        return circunscripcion;
    }

    public void setCircunscripcion(CircunscripcionEntity circunscripcion) {
        this.circunscripcion = circunscripcion;
    }



    public FormacionPoliticaEntity getFormacionPolitica() {
        return formacionPolitica;
    }

    public void setFormacionPolitica(FormacionPoliticaEntity formacionPolitica) {
        this.formacionPolitica = formacionPolitica;
    }

    public EstadoCandidaturaEntity getEstadoCandidatura() {
        return estadoCandidatura;
    }

    public void setEstadoCandidatura(EstadoCandidaturaEntity estadoCandidatura) {
        this.estadoCandidatura = estadoCandidatura;
    }

    public TipoCandidaturaEntity getTipoCandidatura() {
        return tipoCandidatura;
    }

    public void setTipoCandidatura(TipoCandidaturaEntity tipoCandidatura) {
        this.tipoCandidatura = tipoCandidatura;
    }

    public List<CandidatoEntity> getCandidatos() {
        return candidatos;
    }

    public void setCandidatos(List<CandidatoEntity> candidatos) {
        this.candidatos = candidatos;
    }

    @Override
    public String toString() {
        return "CandidaturaEntity{" +
                "id=" + id +
                ", orden=" + orden +
                ", usuarioCreacion='" + usuarioCreacion + '\'' +
                ", fechaCreacion=" + fechaCreacion +
                ", formacionPolitica=" + (formacionPolitica != null ? formacionPolitica.getNombre() : null) +
                ", circunscripcion=" + (circunscripcion != null ? circunscripcion.getNombre() : null) +
                '}';
    }
}
