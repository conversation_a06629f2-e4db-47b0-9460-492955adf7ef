package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.time.LocalDate;
import org.springframework.format.annotation.DateTimeFormat;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Importacion
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-14T09:41:31.919890100+02:00[Europe/Madrid]")
public class Importacion {

  private Long id;

  private String usuarioImportacion;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
  private LocalDate fechaImportacion;

  private String formatoFichero;

  private Boolean importado;

  private Long candidatoValido;

  private Long candidaturaValida;

  private String ipImportacion;

  public Importacion id(Long id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Importacion usuarioImportacion(String usuarioImportacion) {
    this.usuarioImportacion = usuarioImportacion;
    return this;
  }

  /**
   * Get usuarioImportacion
   * @return usuarioImportacion
  */
  
  @Schema(name = "usuarioImportacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("usuarioImportacion")
  public String getUsuarioImportacion() {
    return usuarioImportacion;
  }

  public void setUsuarioImportacion(String usuarioImportacion) {
    this.usuarioImportacion = usuarioImportacion;
  }

  public Importacion fechaImportacion(LocalDate fechaImportacion) {
    this.fechaImportacion = fechaImportacion;
    return this;
  }

  /**
   * Get fechaImportacion
   * @return fechaImportacion
  */
  @Valid 
  @Schema(name = "fechaImportacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("fechaImportacion")
  public LocalDate getFechaImportacion() {
    return fechaImportacion;
  }

  public void setFechaImportacion(LocalDate fechaImportacion) {
    this.fechaImportacion = fechaImportacion;
  }

  public Importacion formatoFichero(String formatoFichero) {
    this.formatoFichero = formatoFichero;
    return this;
  }

  /**
   * Get formatoFichero
   * @return formatoFichero
  */
  
  @Schema(name = "formatoFichero", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("formatoFichero")
  public String getFormatoFichero() {
    return formatoFichero;
  }

  public void setFormatoFichero(String formatoFichero) {
    this.formatoFichero = formatoFichero;
  }

  public Importacion importado(Boolean importado) {
    this.importado = importado;
    return this;
  }

  /**
   * Get importado
   * @return importado
  */
  
  @Schema(name = "importado", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("importado")
  public Boolean getImportado() {
    return importado;
  }

  public void setImportado(Boolean importado) {
    this.importado = importado;
  }

  public Importacion candidatoValido(Long candidatoValido) {
    this.candidatoValido = candidatoValido;
    return this;
  }

  /**
   * Get candidatoValido
   * @return candidatoValido
  */
  
  @Schema(name = "candidatoValido", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("candidatoValido")
  public Long getCandidatoValido() {
    return candidatoValido;
  }

  public void setCandidatoValido(Long candidatoValido) {
    this.candidatoValido = candidatoValido;
  }

  public Importacion candidaturaValida(Long candidaturaValida) {
    this.candidaturaValida = candidaturaValida;
    return this;
  }

  /**
   * Get candidaturaValida
   * @return candidaturaValida
  */
  
  @Schema(name = "candidaturaValida", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("candidaturaValida")
  public Long getCandidaturaValida() {
    return candidaturaValida;
  }

  public void setCandidaturaValida(Long candidaturaValida) {
    this.candidaturaValida = candidaturaValida;
  }

  public Importacion ipImportacion(String ipImportacion) {
    this.ipImportacion = ipImportacion;
    return this;
  }

  /**
   * Get ipImportacion
   * @return ipImportacion
  */
  
  @Schema(name = "ipImportacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("ipImportacion")
  public String getIpImportacion() {
    return ipImportacion;
  }

  public void setIpImportacion(String ipImportacion) {
    this.ipImportacion = ipImportacion;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Importacion importacion = (Importacion) o;
    return Objects.equals(this.id, importacion.id) &&
        Objects.equals(this.usuarioImportacion, importacion.usuarioImportacion) &&
        Objects.equals(this.fechaImportacion, importacion.fechaImportacion) &&
        Objects.equals(this.formatoFichero, importacion.formatoFichero) &&
        Objects.equals(this.importado, importacion.importado) &&
        Objects.equals(this.candidatoValido, importacion.candidatoValido) &&
        Objects.equals(this.candidaturaValida, importacion.candidaturaValida) &&
        Objects.equals(this.ipImportacion, importacion.ipImportacion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, usuarioImportacion, fechaImportacion, formatoFichero, importado, candidatoValido, candidaturaValida, ipImportacion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Importacion {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    usuarioImportacion: ").append(toIndentedString(usuarioImportacion)).append("\n");
    sb.append("    fechaImportacion: ").append(toIndentedString(fechaImportacion)).append("\n");
    sb.append("    formatoFichero: ").append(toIndentedString(formatoFichero)).append("\n");
    sb.append("    importado: ").append(toIndentedString(importado)).append("\n");
    sb.append("    candidatoValido: ").append(toIndentedString(candidatoValido)).append("\n");
    sb.append("    candidaturaValida: ").append(toIndentedString(candidaturaValida)).append("\n");
    sb.append("    ipImportacion: ").append(toIndentedString(ipImportacion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

