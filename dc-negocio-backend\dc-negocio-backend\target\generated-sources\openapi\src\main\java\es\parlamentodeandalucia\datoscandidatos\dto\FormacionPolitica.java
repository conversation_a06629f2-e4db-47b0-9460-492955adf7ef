package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FormacionPolitica
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-14T09:41:31.919890100+02:00[Europe/Madrid]")
public class FormacionPolitica {

  private Long id;

  private String nombre;

  private String siglas;

  private String codigoInterno;

  public FormacionPolitica id(Long id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public FormacionPolitica nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Get nombre
   * @return nombre
  */
  
  @Schema(name = "nombre", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("nombre")
  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public FormacionPolitica siglas(String siglas) {
    this.siglas = siglas;
    return this;
  }

  /**
   * Get siglas
   * @return siglas
  */
  
  @Schema(name = "siglas", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("siglas")
  public String getSiglas() {
    return siglas;
  }

  public void setSiglas(String siglas) {
    this.siglas = siglas;
  }

  public FormacionPolitica codigoInterno(String codigoInterno) {
    this.codigoInterno = codigoInterno;
    return this;
  }

  /**
   * Get codigoInterno
   * @return codigoInterno
  */
  
  @Schema(name = "codigoInterno", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("codigoInterno")
  public String getCodigoInterno() {
    return codigoInterno;
  }

  public void setCodigoInterno(String codigoInterno) {
    this.codigoInterno = codigoInterno;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FormacionPolitica formacionPolitica = (FormacionPolitica) o;
    return Objects.equals(this.id, formacionPolitica.id) &&
        Objects.equals(this.nombre, formacionPolitica.nombre) &&
        Objects.equals(this.siglas, formacionPolitica.siglas) &&
        Objects.equals(this.codigoInterno, formacionPolitica.codigoInterno);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, nombre, siglas, codigoInterno);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FormacionPolitica {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    siglas: ").append(toIndentedString(siglas)).append("\n");
    sb.append("    codigoInterno: ").append(toIndentedString(codigoInterno)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

