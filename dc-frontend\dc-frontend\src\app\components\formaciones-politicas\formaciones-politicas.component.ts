import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { forkJoin } from 'rxjs';
import { ConfirmationService, MessageService } from 'primeng/api';

import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

import { FormacionPoliticaService, PoliticalFormationBackend } from '../../services/formacion-politica.service';
import { CircunscripcionesService, Circunscripcion } from '../../services/circunscripcionService';

// PrimeNG UI Modules
import { TableModule, Table } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { PaginatorModule } from 'primeng/paginator';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SidebarModule } from 'primeng/sidebar';
import { RippleModule } from 'primeng/ripple';



// Interfaz para el modelo visual/formulario
interface DisplayPoliticalFormation {
  id?: number;
  code: string;
  name: string;
  acronym: string;
  constituency: string;
  order: number;
  activeCandidacies: boolean;
  representative: string;
}

interface DropdownOption {
  label: string;
  value: any;
}


@Component({
  selector: 'app-formaciones-politicas',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    HttpClientModule,
    HeaderComponent,
    FooterComponent,
    // PrimeNG
    TableModule,
    ButtonModule,
    InputTextModule,
    DialogModule,
    DropdownModule,
    CheckboxModule,
    IconFieldModule,
    InputIconModule,
    PaginatorModule,
    ConfirmDialogModule,
    SidebarModule,
    RippleModule
  ],
  providers: [ConfirmationService, MessageService],
  templateUrl: './formaciones-politicas.component.html',
  styleUrls: ['./formaciones-politicas.component.scss']
})
export class FormacionesPoliticasComponent implements OnInit {
  @ViewChild('dt') dt: Table | undefined;

  politicalFormations: DisplayPoliticalFormation[] = [];
  selectedRows: DisplayPoliticalFormation[] = [];

  showFormationSidebar = false;
  sidebarTitle = 'Añadir Formación Política';

  newFormation: DisplayPoliticalFormation = {
    code: '',
    name: '',
    acronym: '',
    constituency: '',
    order: 1,
    activeCandidacies: true,
    representative: ''
  };

  constituencies: DropdownOption[] = [];
  globalFilterValue = '';
  showMassiveActions = false;
  openedDropdownId: number | null = null;

  constructor(
    private formacionPoliticaService: FormacionPoliticaService,
    private circunscripcionesService: CircunscripcionesService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService
  ) {}

  ngOnInit(): void {
    this.loadPoliticalFormations();
    this.loadConstituencies();
  }

  loadPoliticalFormations(): void {
    this.formacionPoliticaService.getAll().subscribe({
      next: (data: PoliticalFormationBackend[]) => {
        this.politicalFormations = data.map(f => ({
          id: f.id,
          code: f.codigoInterno,
          name: f.nombre,
          acronym: f.siglas,
          constituency: 'N/A', // Placeholder, se podría mapear si se tiene la información
          order: 0, // Placeholder
          activeCandidacies: false, // Placeholder
          representative: 'N/A' // Placeholder
        }));
        this.dt?.reset();
        this.selectedRows = [];
      },
      error: (err) => console.error('Error loading political formations:', err)
    });
  }

  loadConstituencies(): void {
    this.circunscripcionesService.getAll().subscribe({
      next: (data: Circunscripcion[]) => {
        this.constituencies = data.map(c => ({
          label: c.nombre,
          value: c.nombre
        }));
        console.log('Constituencies loaded:', this.constituencies);
      },
      error: (err) => console.error('Error loading constituencies:', err)
    });
  }

  clearFilters(): void {
    this.globalFilterValue = '';
    this.dt?.clear();
  }

  isRowSelectable(row: { data: DisplayPoliticalFormation }): boolean {
    return true;
  }

  openFormationSidebar(formation: DisplayPoliticalFormation | null): void {
    if (formation) {
      this.sidebarTitle = 'Editar Formación Política';
      this.newFormation = { ...formation };
    } else {
      this.sidebarTitle = 'Añadir Formación Política';
      this.newFormation = {
        code: '',
        name: '',
        acronym: '',
        constituency: '',
        order: 1,
        activeCandidacies: true,
        representative: ''
      };
    }
    this.showFormationSidebar = true;
    this.openedDropdownId = null;
  }

  saveFormation(): void {
    console.log('Saving formation:', this.newFormation);
    this.showFormationSidebar = false;
  }

  toggleDropdown(id: number): void {
    this.openedDropdownId = this.openedDropdownId === id ? null : id;
  }

  activateSelected(): void {
    console.log('Activating selected formations:', this.selectedRows);
    this.showMassiveActions = false;
  }

  deactivateSelected(): void {
    console.log('Deactivating selected formations:', this.selectedRows);
    this.showMassiveActions = false;
  }

  deactivateThisFormation(id: number): void {
    console.log(`Deactivating formation with id: ${id}`);
    this.openedDropdownId = null;
  }

  activateThisFormation(id: number): void {
    console.log(`Activating formation with id: ${id}`);
    this.openedDropdownId = null;
  }
}